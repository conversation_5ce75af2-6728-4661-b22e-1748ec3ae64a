<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSalePeriodRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'date_start_client' => 'required|date',
            'date_end_client' => 'required|date|after_or_equal:date_start_client',
            'date_start_agent' => 'required|date|after_or_equal:date_start_client',
            'date_end_agent' => 'required|date|after_or_equal:date_start_agent|before_or_equal:date_end_client',
            'date_start_validity' => 'required|date',
            'date_end_validity' => 'required|date|after_or_equal:date_start_validity',
            'id_periodicity' => 'required|exists:periodicities,id',
            'id_campaign' => 'required|exists:campaigns,id',
            'id_abn_type' => 'required|exists:subs_types,id',
        ];
    }
}

