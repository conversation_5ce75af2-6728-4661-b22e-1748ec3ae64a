<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSalePeriodRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'date_start_client' => 'sometimes|required|date',
            'date_end_client' => 'sometimes|required|date|after_or_equal:date_start_client',
            'date_start_agent' => 'sometimes|required|date|after_or_equal:date_start_client',
            'date_end_agent' => 'sometimes|required|date|after_or_equal:date_start_agent|before_or_equal:date_end_client',
            'date_start_validity' => 'sometimes|required|date',
            'date_end_validity' => 'sometimes|required|date|after_or_equal:date_start_validity',
            'id_periodicity' => 'sometimes|required|exists:periodicities,id',
            'id_campaign' => 'sometimes|required|exists:campaigns,id',
            'id_abn_type' => 'sometimes|required|exists:subs_types,id',
        ];
    }
}


