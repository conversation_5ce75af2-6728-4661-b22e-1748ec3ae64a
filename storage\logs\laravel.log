[2025-06-17 13:42:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:43:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:44:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:45:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:46:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:47:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:48:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:49:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:50:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:51:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:52:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:53:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:54:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:55:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:56:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:57:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:58:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 13:59:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:00:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:01:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:02:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:03:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:04:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:05:54] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:06:22] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:07:22] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:08:22] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:09:22] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-17 14:10:22] local.ERROR: Uncaught ReflectionException: Class "App\Providers\CampaignPolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\C...')
#1 [internal function]: {closure}('App\\Providers\\C...', 'App\\Providers\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\CampaignPolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\C...')
#1 [internal function]: {closure}('App\\\\Providers\\\\C...', 'App\\\\Providers\\\\C...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
