<?php

namespace Database\Seeders;

use App\Models\Campaign;
use App\Models\Periodicity;
use App\Models\SalePeriod;
use App\Models\SubsType;
use Illuminate\Database\Seeder;

class SalePeriodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all campaigns
        $campaigns = Campaign::all();
        
        // Get all subscription types
        $subsTypes = SubsType::all();

        // get all periodicities
        $periodicities = Periodicity::all();
        
        // Define sale periods data
        $salePeriods = [
            // Back to School Campaign periods
            [
                'nom_fr' => 'Période de vente rentrée scolaire - Abonnement scolaire',
                'nom_en' => 'Back to School Sale Period - School Subscription',
                'nom_ar' => 'فترة بيع العودة إلى المدرسة - اشتراك مدرسي',
                'date_start_client' => '2025-04-25',
                'date_end_client' => '2025-08-25',
                'date_start_agent' => '2025-04-25',
                'date_end_agent' => '2025-08-25',
                'date_start_validity' => '2025-04-25',
                'date_end_validity' => '2025-08-25',
                'id_periodicity' => $periodicities[0]->id,
                'campaign_index' => 0, // Campagne Rentrée Scolaire 2025
                'subs_type_index' => 0, // Abonnement scolaire
            ],
            
            // Summer Campaign periods
            [
                'nom_fr' => 'Période de vente été - Abonnement civil',
                'nom_en' => 'Summer Sale Period - Civil Subscription',
                'nom_ar' => 'فترة بيع الصيف - اشتراك مدني',
                'date_start_client' => '2025-04-25',
                'date_end_client' => '2025-08-25',
                'date_start_agent' => '2025-04-25',
                'date_end_agent' => '2025-08-25',
                'date_start_validity' => '2025-04-25',
                'date_end_validity' => '2025-08-25',
                'id_periodicity' => $periodicities[1]->id,
                'campaign_index' => 1, // Campagne Été 2025
                'subs_type_index' => 2, // Abonnement civil
            ],

            // Special Student Campaign periods
            [
                'nom_fr' => 'Période spéciale étudiants - Abonnement universitaire',
                'nom_en' => 'Special Student Period - University Subscription',
                'nom_ar' => 'فترة خاصة للطلاب - اشتراك جامعي',
                'date_start_client' => '2025-04-25',
                'date_end_client' => '2025-08-25',
                'date_start_agent' => '2025-04-25',
                'date_end_agent' => '2025-08-25',
                'date_start_validity' => '2025-04-25',
                'date_end_validity' => '2025-08-25',
                'id_periodicity' => $periodicities[2]->id,
                'campaign_index' => 3, // Campagne Spéciale Étudiants
                'subs_type_index' => 1, // Abonnement universitaire
            ],
            
            // Promotional Campaign periods (inactive)
            [
                'nom_fr' => 'Période promotionnelle - Tous abonnements',
                'nom_en' => 'Promotional Period - All Subscriptions',
                'nom_ar' => 'فترة ترويجية - جميع الاشتراكات',
                'date_start_client' => '2025-01-01',
                'date_end_client' => '2025-01-05',
                'date_start_agent' => '2025-01-01',
                'date_end_agent' => '2025-01-05',
                'date_start_validity' => '2025-01-01',
                'date_end_validity' => '2025-01-05',
                'id_periodicity' => $periodicities[3]->id,
                'campaign_index' => 4,
                'subs_type_index' => 0,
            ]
        ];
        
        foreach ($salePeriods as $salePeriod) {
            if (isset($campaigns[$salePeriod['campaign_index']]) && isset($subsTypes[$salePeriod['subs_type_index']])) {
                SalePeriod::create([
                    'nom_fr' => $salePeriod['nom_fr'],
                    'nom_en' => $salePeriod['nom_en'],
                    'nom_ar' => $salePeriod['nom_ar'],
                    'date_start_client' => $salePeriod['date_start_client'],
                    'date_end_client' => $salePeriod['date_end_client'],
                    'date_start_agent' => $salePeriod['date_start_agent'],
                    'date_end_agent' => $salePeriod['date_end_agent'],
                    'date_start_validity' => $salePeriod['date_start_validity'],
                    'date_end_validity' => $salePeriod['date_end_validity'],
                    'id_periodicity' => $salePeriod['id_periodicity'],
                    'id_campaign' => $campaigns[$salePeriod['campaign_index']]->id,
                    'id_abn_type' => $subsTypes[$salePeriod['subs_type_index']]->id,
                ]);
            }
        }
    }
}

