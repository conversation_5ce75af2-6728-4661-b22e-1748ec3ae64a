<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLineStationAssignmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_line' => 'required|exists:lines,id',
            'stations' => 'required|array',
            'stations.*.id_station' => 'required|exists:stations,id',
            'stations.*.position' => 'required|integer|min:1',
            'stations.*.type' => 'required|in:HIDDEN,TERMINUS,INTER',
            'stations.*.has_departures' => 'required|boolean',
            'stations.*.departure_configs' => 'present|array',
            'stations.*.departure_configs.*.option' => 'required_if:stations.*.has_departures,true|integer|min:1',
            'stations.*.departure_configs.*.advancement_position' => 'required_if:stations.*.has_departures,true|integer|min:1',
            'stations.*.departure_configs.*.direction' => 'required_if:stations.*.has_departures,true|integer|min:1',
            'stations.*.departure_configs.*.time' => 'required_if:stations.*.has_departures,true|date_format:H:i',

            'routes' => 'required|array',
            'routes.*.number_of_km' => 'required|numeric|min:0',
            'routes.*.id_station_start' => 'required|exists:stations,id',
            'routes.*.id_station_end' => 'required|exists:stations,id',
            'routes.*.inter_station' => 'required|boolean',
            'routes.*.status' => 'required|boolean'
        ];
    }
}

